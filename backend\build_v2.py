#!/usr/bin/env python3
"""
YF AI Chat Backend v2 打包脚本
高性能版本，包含Windows效能模式优化和连接错误修复
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("  YF AI Chat Backend v2 - 高性能版本打包")
    print("=" * 70)
    print("🚀 新功能:")
    print("   ⚡ Windows高性能模式自动优化")
    print("   🛡️ 防止进入效能模式")
    print("   🔧 连接错误智能处理")
    print("   📊 增强的性能监控")
    print("   🎯 优化的网络参数")
    print("=" * 70)

def check_environment():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"   Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("   ❌ Python版本过低，建议使用Python 3.8+")
        return False
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"   ✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("   ❌ PyInstaller未安装")
        print("   请运行: pip install pyinstaller")
        return False
    
    # 检查必要的依赖
    required_packages = [
        'fastapi', 'uvicorn', 'pydantic', 'sqlalchemy', 
        'aiosqlite', 'langchain', 'psutil'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package}")
    
    if missing_packages:
        print(f"   缺少依赖包: {', '.join(missing_packages)}")
        print("   请运行: pip install -r requirements.txt")
        return False
    
    # 检查必要文件
    required_files = [
        'main.py',
        'power_management.py',
        'exe_performance_wrapper.py',
        'logging_config.py',
        'YF_AI_Chat_Backend_v2.spec'
    ]
    
    for file in required_files:
        if not Path(file).exists():
            print(f"   ❌ 缺少文件: {file}")
            return False
        else:
            print(f"   ✅ {file}")
    
    print("   ✅ 环境检查通过")
    return True

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")

    dirs_to_clean = ['build', 'dist/__pycache__']
    files_to_clean = ['*.pyc', '*.pyo']

    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            try:
                shutil.rmtree(dir_name)
                print(f"   🗑️ 已删除: {dir_name}")
            except PermissionError:
                print(f"   ⚠️ 无法删除: {dir_name} (权限不足)")

    # 清理Python缓存文件
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs[:]:
            if dir_name == '__pycache__':
                try:
                    shutil.rmtree(Path(root) / dir_name)
                    dirs.remove(dir_name)
                    print(f"   🗑️ 已删除: {Path(root) / dir_name}")
                except PermissionError:
                    print(f"   ⚠️ 无法删除: {Path(root) / dir_name} (权限不足)")
                    dirs.remove(dir_name)  # 仍然从列表中移除以避免重复处理

    print("   ✅ 清理完成")

def run_pyinstaller():
    """运行PyInstaller打包"""
    print("📦 开始打包...")
    
    # PyInstaller命令
    cmd = [
        'pyinstaller',
        '--clean',  # 清理临时文件
        '--noconfirm',  # 不询问覆盖
        'YF_AI_Chat_Backend_v2.spec'
    ]
    
    print(f"   执行命令: {' '.join(cmd)}")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 运行PyInstaller
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=Path.cwd()
        )
        
        # 记录结束时间
        end_time = time.time()
        build_time = end_time - start_time
        
        if result.returncode == 0:
            print(f"   ✅ 打包成功！耗时: {build_time:.1f}秒")
            return True
        else:
            print(f"   ❌ 打包失败！")
            print("   错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"   ❌ 打包过程出错: {e}")
        return False

def create_v2_package():
    """创建v2版本包"""
    print("📁 创建v2版本包...")
    
    dist_dir = Path('dist')
    exe_file = dist_dir / 'YF_AI_Chat_Backend_v2.exe'
    
    if not exe_file.exists():
        print("   ❌ 找不到打包后的exe文件")
        return False
    
    # 创建v2版本目录
    v2_dir = dist_dir / 'YF_AI_Chat_Backend_v2_便携版'
    if v2_dir.exists():
        shutil.rmtree(v2_dir)
    
    v2_dir.mkdir(exist_ok=True)
    
    # 复制exe文件
    shutil.copy2(exe_file, v2_dir / 'YF_AI_Chat_Backend_v2.exe')
    print(f"   ✅ 已复制: YF_AI_Chat_Backend_v2.exe")
    
    # 复制配置文件
    config_files = [
        '.env.example',
        'Windows高性能模式使用说明.md',
        '连接错误修复说明.md',
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, v2_dir / config_file)
            print(f"   ✅ 已复制: {config_file}")
    
    # 创建启动脚本
    start_script = v2_dir / '启动高性能模式.bat'
    with open(start_script, 'w', encoding='utf-8') as f:
        f.write('''@echo off
chcp 65001 >nul
title YF AI Chat Backend v2 - 高性能模式

echo.
echo ============================================================
echo   YF AI Chat Backend v2 - 高性能版本
echo ============================================================
echo.
echo 🚀 新功能特性:
echo    ⚡ 自动Windows高性能优化
echo    🛡️  防止进入效能模式
echo    🔧 智能连接错误处理
echo    📊 增强的性能监控
echo.
echo 💡 建议以管理员身份运行以获得最佳性能
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 检测到管理员权限，将启用完整优化
) else (
    echo ⚠️  未检测到管理员权限，某些优化功能可能受限
    echo 💡 建议右键选择"以管理员身份运行"
)

echo.
echo 🚀 启动YF AI Chat Backend v2...
echo.

YF_AI_Chat_Backend_v2.exe

echo.
echo 👋 服务已停止
pause
''')
    
    print(f"   ✅ 已创建: 启动高性能模式.bat")
    
    # 创建使用说明
    readme_file = v2_dir / 'README_v2.txt'
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(f'''YF AI Chat Backend v2 - 高性能版本
==========================================

版本信息:
- 版本号: v2.0.0
- 构建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- 构建平台: Windows

🚀 v2版本新功能:
================
⚡ Windows高性能模式自动优化
🛡️ 防止系统进入效能模式
🔧 智能连接错误处理和过滤
📊 增强的性能监控和状态显示
🎯 优化的网络参数和并发处理
🌐 改进的WordPress插件通信性能

📁 文件说明:
============
YF_AI_Chat_Backend_v2.exe          - 主程序文件
启动高性能模式.bat                   - 高性能启动脚本
.env.example                        - 环境配置示例
Windows高性能模式使用说明.md         - 详细使用说明
连接错误修复说明.md                  - 问题修复说明

🚀 快速开始:
============
1. 复制 .env.example 为 .env
2. 编辑 .env 文件配置你的API密钥
3. 右键选择"以管理员身份运行" -> 启动高性能模式.bat
4. 或直接双击 YF_AI_Chat_Backend_v2.exe

💡 性能提示:
============
- 建议以管理员身份运行以获得最佳性能
- 高性能模式会自动优化Windows电源设置
- 程序退出时会自动恢复原始电源配置
- 支持实时性能监控和状态查看

🔧 技术支持:
============
如有问题请查看详细说明文档或联系技术支持

版权所有 © 2024 YF Company
''')
    
    print(f"   ✅ 已创建: README_v2.txt")
    
    # 显示包大小
    exe_size = exe_file.stat().st_size / 1024 / 1024
    print(f"   📊 exe文件大小: {exe_size:.1f}MB")
    
    print(f"   ✅ v2版本包创建完成: {v2_dir}")
    return True

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return False
    
    print()
    
    # 清理构建目录
    clean_build_dirs()
    print()
    
    # 运行打包
    if not run_pyinstaller():
        print("\n❌ 打包失败")
        return False
    
    print()
    
    # 创建v2版本包
    if not create_v2_package():
        print("\n❌ 创建版本包失败")
        return False
    
    print()
    print("🎉 YF AI Chat Backend v2 打包完成！")
    print("=" * 70)
    print("📁 输出文件:")
    print("   dist/YF_AI_Chat_Backend_v2.exe")
    print("   dist/YF_AI_Chat_Backend_v2_便携版/")
    print()
    print("🚀 使用方法:")
    print("   1. 进入 dist/YF_AI_Chat_Backend_v2_便携版/ 目录")
    print("   2. 配置 .env 文件")
    print("   3. 右键以管理员身份运行 '启动高性能模式.bat'")
    print()
    print("💡 v2版本将自动优化Windows性能，解决效能模式问题！")
    print("=" * 70)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
